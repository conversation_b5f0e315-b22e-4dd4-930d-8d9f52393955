package com.byzaneo.generix.rest.service.external;

import com.byzaneo.angular.bean.I18NTranslation;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.generix.api.dto.translation.*;
import com.byzaneo.generix.api.service.external.delegators.TranslationServiceDelegator;
import com.byzaneo.generix.api.service.internal.impl.translation.TranslationService;
import com.byzaneo.generix.api.util.PermissionHelper;
import com.byzaneo.generix.api.util.RestServiceHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.api.Right;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.*;
import com.byzaneo.xtrade.service.DocumentErrorTranslationService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.slf4j.LoggerFactory.getLogger;


@Component
@RequiredArgsConstructor
public class TranslationServiceDelegatorImpl implements TranslationServiceDelegator {

    private static final Logger log = getLogger(TranslationServiceDelegatorImpl.class);
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH.mm.ss.SSS");

    @Autowired
    @Qualifier(SecurityService.SERVICE_NAME)
    private transient SecurityService securityService;

    @Autowired
    private PermissionHelper permissionHelper;

    @Autowired
    private TranslationService translationService;

    @Autowired
    private I18NService i18NService;

    @Autowired
    private DocumentErrorTranslationService documentErrorTranslationService;

    @Autowired
    @Qualifier(I18nTranslationDAO.DAO_NAME)
    private I18nTranslationDAO i18nTranslationDAO;

    @Autowired
    @Qualifier(AccountService.SERVICE_NAME)
    private AccountService accountService;



    private static final String DELIMITER = ";";
    private static final Set<String> PROTECTED_LANGUAGES = Set.of("fr", "en");

    @Override
    public Response downloadDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang) {
        requestId = ensureRequestId(requestId);

        log.info("""
            [{}] Received download dictionary request:
            - Type: {}
            - Language: {}
            """, requestId, dictType, lang);

        var error = validateRequest(request, requestId, dictType, Right.READ);
        if (error != null) {
            log.warn("[{}] Request validation failed: {}", requestId, error.getEntity());
            return error;
        }

        var requestedLang = parseAndValidateLangCode(lang, requestId);
        log.debug("[{}] Validated language: {}", requestId, requestedLang);

        var response = switch (dictType.toLowerCase()) {
            case "errors" -> translationService.exportTranslationsWithLang(requestedLang);
            case "frontend" -> exportFrontendTranslations(requestedLang, requestId);
            default -> throw new IllegalArgumentException("Unsupported dictionary type: " + dictType);
        };

        log.info("[{}] Successfully processed dictionary download request.", requestId);
        return response;
    }

    @Override
    public Response importDictionary(HttpServletRequest request, UUID requestId, String dictType, String lang, InputStream csvContent) {
        requestId = ensureRequestId(requestId);

        log.info("""
            [{}] Received import dictionary request:
            - Type: {}
            - Language: {}
            """, requestId, dictType, lang);

        var error = validateRequest(request, requestId, dictType, Right.UPDATE);
        if (error != null) {
            log.warn("[{}] Request validation failed: {}", requestId, error.getEntity());
            return error;
        }

        var requestedLang = parseAndValidateLangCode(lang, requestId);
        log.debug("[{}] Validated language: {}", requestId, requestedLang);


        var response = switch (dictType.toLowerCase()) {
            case "errors" -> translationService.importTranslationsWithLang(requestedLang, csvContent);
            case "frontend" -> importFrontendTranslations(requestedLang, csvContent, requestId, request);
            default -> throw new IllegalArgumentException("Unsupported dictionary type: " + dictType);
        };

        log.info("[{}] Successfully processed dictionary import request.", requestId);
        return response;
    }

    private Response validateRequest(HttpServletRequest request, UUID requestId, String dictType, Right right) {
        log.debug("[{}] Validating request with dictType: {} and right: {}", requestId, dictType, right);

        if (!RestServiceHelper.hasBearerToken(request)) {
            log.warn("[{}] Missing or empty Authorization header.", requestId);
            return RestServiceHelper.getResponseOnError(
                Response.Status.FORBIDDEN.toString(),
                "Authorization header must not be null or empty",
                Response.Status.FORBIDDEN,
                requestId
            );
        }

        var isValidDictType = switch (dictType.toLowerCase()) {
            case "errors", "frontend" -> true;
            default -> false;
        };

        if (!isValidDictType) {
            log.warn("[{}] Invalid dictionary type: {}", requestId, dictType);
            return RestServiceHelper.getResponseOnError(
                Response.Status.BAD_REQUEST.toString(),
                "Invalid dictionary type. Must be 'errors' or 'frontend'",
                Response.Status.BAD_REQUEST,
                requestId
            );
        }

        TechnicalUser technicalUser = securityService.getTechnicalUserById(
            RestServiceHelper.getTechnicalUserIdFromRequest(request)
        );
        log.debug("[{}] Fetched technical user: {}", requestId, technicalUser != null ? technicalUser.getId() : "null");

        boolean hasPermission = permissionHelper.isGrantedTechnicalUser(
            com.byzaneo.generix.service.SecurityService.Resource.Api_permissions_translation,
            right,
            technicalUser
        );

        if (!hasPermission || technicalUser.isDisabled()) {
            log.warn("[{}] User does not have required permission or is disabled.", requestId);
            return RestServiceHelper.getResponseOnError(
                Response.Status.FORBIDDEN.toString(),
                "Security error: You don't have the rights to access to the API.",
                Response.Status.FORBIDDEN,
                requestId
            );
        }

        return null;
    }

    private UUID ensureRequestId(UUID requestId) {
        if (requestId == null) {
            UUID newId = UUID.randomUUID();
            log.debug("Generated new requestId: {}", newId);
            return newId;
        }
        return requestId;
    }

    private String parseAndValidateLangCode(String lang, UUID requestId) {
        if (lang == null || lang.isBlank()) {
            log.debug("[{}] No language code provided. Returning null.", requestId);
            return null;
        }

        var trimmedLang = lang.trim();

        if (!LanguageConstants.ALLOWED_LANG_CODES.contains(trimmedLang)) {
            var errorMessage = """
                Invalid language code: %s.
                Must be a valid ISO 639-1 language code.
                """.formatted(trimmedLang);
            log.error("[{}] {}", requestId, errorMessage.replace("\n", " "));
            throw new IllegalArgumentException(errorMessage);
        }

        return trimmedLang;
    }

    private Response exportFrontendTranslations(String requestedLang, UUID requestId) {
        try {
            log.debug("[{}] Exporting frontend translations for language: {}", requestId, requestedLang);

            var translations = i18NService.getTranslations();
            var availableLocales = i18NService.getLocales();

            var exportLocales = requestedLang != null && !requestedLang.isBlank()
                ? availableLocales.stream()
                    .filter(locale -> requestedLang.equalsIgnoreCase(locale.getLanguage()))
                    .toList()
                : availableLocales;

            var csvContent = generateFrontendCsv(translations, exportLocales, requestId);
            var csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);

            var filename = "frontend_translations.csv";

            return Response.ok(csvBytes, "application/octet-stream")
                .header("Content-Disposition", "attachment; filename=" + filename)
                .header("Content-Length", csvBytes.length)
                .build();

        } catch (Exception e) {
            log.error("[{}] Error exporting frontend translations: {}", requestId, e.getMessage(), e);
            return RestServiceHelper.getResponseOnError(
                Response.Status.INTERNAL_SERVER_ERROR.toString(),
                "Failed to export frontend translations: " + e.getMessage(),
                Response.Status.INTERNAL_SERVER_ERROR,
                requestId
            );
        }
    }

    private Response importFrontendTranslations(String requestedLang, InputStream csvContent, UUID requestId, HttpServletRequest request) {
        var errors = new ArrayList<ImportError>();
        var totalImported = 0;

        try {
            log.debug("[{}] Importing frontend translations for language: {}", requestId, requestedLang);

            // Enhanced CSV parsing with proper encoding handling for special characters
            var csvContentString = new String(csvContent.readAllBytes(), StandardCharsets.UTF_8);
            // Normalize line endings and split
            var lines = csvContentString.replace("\r\n", "\n").replace("\r", "\n").split("\n");

            if (lines.length < 2) {
                var error = ImportError.builder()
                    .lineNumber(0)
                    .errorMessage("File must contain at least a header and one data row")
                    .build();
                errors.add(error);

                var response = DictionaryImportResponse.builder()
                    .totalImported(0)
                    .errors(errors)
                    .build();

                return Response.status(Response.Status.CREATED).entity(response).build();
            }

            totalImported = processFrontendCsvLines(lines, requestedLang, errors, requestId, request);

            // Count only actual errors (not success messages which have lineNumber = 0)
            var actualErrorCount = (int) errors.stream()
                .filter(error -> error.getLineNumber() != 0)
                .count();

            var response = DictionaryImportResponse.builder()
                .totalImported(totalImported)
                .errors(errors)
                .build();

            log.info("[{}] Frontend import completed. Imported: {}, Errors: {}", requestId, totalImported, actualErrorCount);
            return Response.status(Response.Status.CREATED).entity(response).build();

        } catch (Exception e) {
            log.error("[{}] Error importing frontend translations: {}", requestId, e.getMessage(), e);
            return RestServiceHelper.getResponseOnError(
                Response.Status.INTERNAL_SERVER_ERROR.toString(),
                "Failed to import frontend translations: " + e.getMessage(),
                Response.Status.INTERNAL_SERVER_ERROR,
                requestId
            );
        }
    }

    private String generateFrontendCsv(List<I18NTranslationDto> translations, List<Locale> exportLocales, UUID requestId) {
        var csvBuilder = new StringBuilder();

        var sortedLocales = exportLocales.stream()
            .sorted(Comparator.comparing(Locale::toString))
            .toList();

        csvBuilder.append("module").append(DELIMITER).append("key");
        for (var locale : sortedLocales) {
            csvBuilder.append(DELIMITER).append(locale.toString());
        }
        csvBuilder.append("\n");

        // First, we need to get module names for each translation
        var moduleMap = new HashMap<Long, String>();
        var allModules = i18NService.findAllI18NModules();
        allModules.forEach(module -> moduleMap.put(module.getId(), module.getName()));

        var translationMap = translations.stream()
            .filter(t -> moduleMap.containsKey(t.getI18NModuleId()))
            .collect(Collectors.groupingBy(
                t -> moduleMap.get(t.getI18NModuleId()) + ":" + t.getCode(),
                Collectors.toMap(
                    t -> t.getLocale().toString(),
                    t -> t.getNewValue() != null ? t.getNewValue() : (t.getDefaultValue() != null ? t.getDefaultValue() : ""),
                    (existing, replacement) -> replacement
                )
            ));

        translationMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                var moduleKey = entry.getKey().split(":", 2);
                var module = moduleKey[0];
                var key = moduleKey[1];
                var localeValues = entry.getValue();

                csvBuilder.append(module).append(DELIMITER).append(key);
                for (var locale : sortedLocales) {
                    var value = localeValues.getOrDefault(locale.toString(), "");
                    csvBuilder.append(DELIMITER).append(escapeCsvValue(value));
                }
                csvBuilder.append("\n");
            });

        log.debug("[{}] Generated CSV with {} rows", requestId, translationMap.size());
        return csvBuilder.toString();
    }

    private int processFrontendCsvLines(String[] lines, String requestedLang, List<ImportError> errors, UUID requestId, HttpServletRequest request) {
        var totalImported = 0;
        var headers = parseCsvLine(lines[0]);

        if (headers.length < 3 || !"module".equals(headers[0]) || !"key".equals(headers[1])) {
            errors.add(ImportError.builder()
                .lineNumber(1)
                .errorMessage("Invalid header format. Expected: module;key;language1;language2;...")
                .build());
            return 0;
        }

        // Enhanced locale validation with detailed error reporting
        var langColumnIndex = new HashMap<String, Integer>();
        for (var i = 2; i < headers.length; i++) {
            var langCode = headers[i].trim().toLowerCase();

            // Check if locale is valid
            if (!LanguageConstants.ALLOWED_LANG_CODES.contains(langCode)) {
                errors.add(ImportError.builder()
                    .lineNumber(1)
                    .errorMessage("Error [column " + (i + 1) + "]: The locale " + langCode + " is not a recognized locale. The column is ignored.")
                    .build());
                continue;
            }

            if (requestedLang == null || requestedLang.isBlank() || requestedLang.equalsIgnoreCase(langCode)) {
                langColumnIndex.put(langCode, i);
            }
        }

        // Process data rows
        for (var lineNumber = 2; lineNumber <= lines.length; lineNumber++) {
            var line = lines[lineNumber - 1].trim();
            if (line.isEmpty()) continue;

            var parts = parseCsvLine(line);
            if (parts.length < 3) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("Row must have at least module, key, and one translation")
                    .build());
                continue;
            }

            var module = parts[0].trim();
            var key = parts[1].trim();

            if (module.isEmpty() || key.isEmpty()) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("Module and key cannot be empty")
                    .build());
                continue;
            }

            totalImported += processTranslationRow(module, key, parts, langColumnIndex, errors, lineNumber, requestId, request);
        }

        return totalImported;
    }

    private int processTranslationRow(String module, String key, String[] parts, Map<String, Integer> langColumnIndex,
                                    List<ImportError> errors, int lineNumber, UUID requestId, HttpServletRequest request) {
        var imported = 0;

        // Find module
        var moduleOpt = i18NService.findI18NModuleByName(module);
        if (moduleOpt.isEmpty()) {
            errors.add(ImportError.builder()
                .lineNumber(lineNumber)
                .errorMessage("Error [line " + lineNumber + "]: Code " + key + " (Module Name = " + module + ") does not exist. The line is ignored.")
                .build());
            return 0;
        }

        var moduleEntity = moduleOpt.get();

        // Enhanced validation: Check if key exists for French and English
        if (!isKeyExistingForProtectedLanguages(moduleEntity.getId(), key)) {
            errors.add(ImportError.builder()
                .lineNumber(lineNumber)
                .errorMessage("Error [line " + lineNumber + "]: Code " + key + " (Module Name = " + module + ") does not exist. The line is ignored.")
                .build());
            return 0;
        }

        for (var entry : langColumnIndex.entrySet()) {
            var langCode = entry.getKey();
            var columnIndex = entry.getValue();

            if (columnIndex >= parts.length) continue;

            var value = parts[columnIndex].trim();
            if (value.isEmpty()) continue;

            if (PROTECTED_LANGUAGES.contains(langCode)) {
                log.debug("[{}] Skipping protected language: {}", requestId, langCode);
                continue;
            }

            try {
                updateOrCreateTranslation(moduleEntity.getId(), key, langCode, value, requestId, request);
                imported++;
            } catch (Exception e) {
                errors.add(ImportError.builder()
                    .lineNumber(lineNumber)
                    .errorMessage("Failed to import translation for key '" + key + "' and language '" + langCode + "': " + e.getMessage())
                    .build());
            }
        }

        return imported;
    }

    private void updateOrCreateTranslation(Long moduleId, String key, String langCode, String value, UUID requestId, HttpServletRequest request) {
        var locale = new Locale(langCode);

        User actualUser = null;
        try {
            var username = RestServiceHelper.getJWTUsername(request);
            if (username != null) {
                actualUser = accountService.getUserByLoginOrOpenID(username);
                if (actualUser != null && actualUser.getSwitchUserLogin() != null) {
                    actualUser = accountService.getUserByLoginOrOpenID(actualUser.getSwitchUserLogin());
                }
            }
        } catch (Exception e) {
            log.warn("[{}] Could not extract user from JWT token: {}", requestId, e.getMessage());
        }

        // Fallback to admin user if no user found from token
        if (actualUser == null) {
            actualUser = accountService.getUserByLoginOrOpenID("admin");
            log.debug("[{}] Using admin user as fallback for technical operation tracking", requestId);
        }

        if (actualUser == null) {
            log.warn("[{}] No user found for tracking translation changes", requestId);
        }

        // Find existing translation using correct property names
        var query = new Query(new AndClause(
            Clauses.equal("code", key),
            Clauses.equal("locale", locale),
            Clauses.equal("i18NModule.id", moduleId)
        ));

        var existingTranslations = i18nTranslationDAO.search(query);

        if (!existingTranslations.isEmpty()) {
            // Update existing
            var existing = existingTranslations.get(0);
            existing.setNewValue(value);
            existing.setNewValueChangedAt(new Date());
            existing.setNewValueChangedBy(actualUser);
            i18nTranslationDAO.store(existing);
            log.debug("[{}] Updated translation for key: {}, locale: {} by user: {}",
                requestId, key, langCode, actualUser != null ? actualUser.getLogin() : "unknown");
        } else {
            // Create new - need to get the module entity
            var moduleOpt = i18NService.findI18NModuleById(moduleId);
            if (moduleOpt != null) {
                var newTranslation = new I18NTranslation();
                newTranslation.setCode(key);
                newTranslation.setI18NModule(moduleOpt); // Set the module entity, not just ID
                newTranslation.setLocale(locale);
                newTranslation.setNewValue(value);
                newTranslation.setNewValueChangedAt(new Date());
                newTranslation.setNewValueChangedBy(actualUser);
                i18nTranslationDAO.store(newTranslation);
                log.debug("[{}] Created new translation for key: {}, locale: {} by user: {}",
                    requestId, key, langCode, actualUser != null ? actualUser.getLogin() : "unknown");
            } else {
                log.error("[{}] Module with ID {} not found", requestId, moduleId);
                throw new RuntimeException("Module not found: " + moduleId);
            }
        }
    }

    private boolean isKeyExistingForProtectedLanguages(Long moduleId, String key) {
        for (String protectedLang : PROTECTED_LANGUAGES) {
            var query = new Query(new AndClause(
                Clauses.equal("code", key),
                Clauses.equal("locale", new Locale(protectedLang)),
                Clauses.equal("i18NModule.id", moduleId)
            ));

            var existingTranslations = i18nTranslationDAO.search(query);
            if (existingTranslations.isEmpty()) {
                return false; // Key doesn't exist for this protected language
            }
        }
        return true; // Key exists for all protected languages
    }

    private String[] parseCsvLine(String line) {
        var parts = new ArrayList<String>();
        var current = new StringBuilder();
        var inQuotes = false;
        var i = 0;

        while (i < line.length()) {
            var c = line.charAt(i);

            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // Escaped quote
                    current.append('"');
                    i += 2;
                } else {
                    // Toggle quote state
                    inQuotes = !inQuotes;
                    i++;
                }
            } else if (c == ';' && !inQuotes) {
                // Field separator
                parts.add(current.toString());
                current.setLength(0);
                i++;
            } else {
                current.append(c);
                i++;
            }
        }

        // Add the last field
        parts.add(current.toString());

        return parts.toArray(new String[0]);
    }

    private String escapeCsvValue(String value) {
        if (value == null) return "";

        return value.contains(DELIMITER) || value.contains("\"") || value.contains("\n")
            ? "\"" + value.replace("\"", "\"\"") + "\""
            : value;
    }

}
