package com.byzaneo.generix.api.service.internal;

import com.byzaneo.generix.service.repository.service.translation.CriteriaDto;
import com.byzaneo.generix.service.repository.service.translation.UpdateTranslationDto;
import com.byzaneo.generix.xtrade.util.File;
import com.hazelcast.spi.impl.operationservice.impl.responses.ErrorResponse;
import io.swagger.annotations.*;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.apache.cxf.jaxrs.ext.multipart.Multipart;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Api(tags = "gnxRestTranslationService", description = "Operations about Translation services")
@Path(RestTranslationService.SERVICE_PATH)
public interface RestTranslationService {

  public static final String SERVICE_NAME = "gnxRestTranslationService";

  /**
   * Path to access the link service
   */
  public static final String SERVICE_PATH = "";

  @GET
  @Path("/v1/translations")
  @Consumes("application/json")
  @Produces({ "application/json" })
  @ApiOperation(value = "search list of translations by locale")
  @ApiResponses(value = {
      @ApiResponse(code = 500, message = "Internal error while searching translations"),
      @ApiResponse(code = 404, message = "Translations not found"),
      @ApiResponse(code = 200, message = "Success"),
      @ApiResponse(code = 401, message = "Operation not permitted"),
  })
  Response findI18NTranslations(@QueryParam("locale") @NotNull String locale);

  @GET
  @Path("/v1/translation/modules")
  @Consumes("application/json")
  @Produces({ "application/json", "text/plain" })
  @ApiOperation(value = "search list of modules")
  @ApiResponses(value = {
      @ApiResponse(code = 500, message = "Internal error while searching modules"),
      @ApiResponse(code = 404, message = "modules not found"),
      @ApiResponse(code = 200, message = "Success"),
      @ApiResponse(code = 401, message = "Operation not permitted"),
  })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response findModules(@Context HttpServletRequest request);

  @POST
  @Path("/v1/translation/management")
  @Consumes("application/json")
  @Produces({ "application/json" })
  @ApiOperation(value = "search list of translations using filters")
  @ApiResponses(value = {
      @ApiResponse(code = 500, message = "Internal error while searching translations"),
      @ApiResponse(code = 404, message = "translations not found"),
      @ApiResponse(code = 400, message = "Bad request"),
      @ApiResponse(code = 200, message = "Success"),
      @ApiResponse(code = 401, message = "Operation not permitted"),
  })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response findI18NTranslations(@Context HttpServletRequest request, @RequestBody CriteriaDto criteria);

  @PATCH
  @Path("/v1/translation/management/{translation-id}")
  @Consumes("application/json")
  @Produces({ "application/json" })
  @ApiOperation(value = "update the value of the translation")
  @ApiResponses(value = {
      @ApiResponse(code = 500, message = "Internal error while updating the value of the translation"),
      @ApiResponse(code = 404, message = "translations not found"),
      @ApiResponse(code = 400, message = "Bad request"),
      @ApiResponse(code = 200, message = "Success"),
      @ApiResponse(code = 401, message = "Operation not permitted"),
  })
  @ApiImplicitParams({
      @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response editTranslation(@Context HttpServletRequest request, @PathParam("translation-id") Long id, @RequestBody UpdateTranslationDto i18NTranslation);

  @GET
  @Path("/v1/translation/management/languages")
  @Consumes("application/json")
  @Produces({ "application/json"})
  @ApiOperation(value = "search list of languages")
  @ApiResponses(value = {
          @ApiResponse(code = 500, message = "Internal error while searching languages"),
          @ApiResponse(code = 404, message = "languages not found"),
          @ApiResponse(code = 200, message = "Success"),
          @ApiResponse(code = 401, message = "Operation not permitted"),
  })
  @ApiImplicitParams({
          @ApiImplicitParam(name = "Authorization", value = "Access Token", required = true, paramType = "header", example = "Bearer access_token")
  })
  Response findLanguages(@Context HttpServletRequest request);

  @POST
  @Path("/v1/translations/export")
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  @ApiOperation(value = "Export Translation Data", notes = "Export translation data as an CSV file")
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "File exported successfully", response = File.class),
          @ApiResponse(code = 400, message = "Bad Request - Invalid input or parameters"),
          @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
          @ApiResponse(code = 404, message = "Not Found - The requested resource could not be found"),
          @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class),
  })
  Response exportTranslationsData(@Context HttpServletRequest request, @RequestBody CriteriaDto criteria);

  @POST
  @Path("/v1/translations/import")
  @Produces(MediaType.MULTIPART_FORM_DATA)
  @ApiOperation(value = "Import Translation Data", notes = "Import translation data from an CSV file")
  @ApiResponses(value = {
          @ApiResponse(code = 200, message = "File imported successfully", response = File.class),
          @ApiResponse(code = 400, message = "Bad Request - Invalid input or parameters"),
          @ApiResponse(code = 403, message = "Forbidden: Access denied due to lack of permissions or incorrect token"),
          @ApiResponse(code = 404, message = "Not Found - The requested resource could not be found"),
          @ApiResponse(code = 500, message = "Internal Server Error - An unexpected error occurred on the server", response = ErrorResponse.class),
  })
  Response importTranslationsData(@Context HttpServletRequest request, @QueryParam("locale") @NotNull String locale, @Multipart(value = "file") Attachment file);

}

