/**
 * 
 */
package com.byzaneo.generix.api.service.internal.impl.translation;

import com.byzaneo.angular.bean.*;
import com.byzaneo.angular.dao.I18nTranslationDAO;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.api.dto.translation.*;
import com.byzaneo.generix.api.util.RestServiceHelper;
import com.byzaneo.generix.service.repository.service.I18NService;
import com.byzaneo.generix.service.repository.service.translation.I18NTranslationDto;
import com.byzaneo.generix.util.LanguageConstants;
import com.byzaneo.generix.xtrade.util.ExcelExportHelper;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.service.AccountService;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentErrorTranslationService;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.jaxrs.ext.multipart.Attachment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.ws.rs.core.Response;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.byzaneo.commons.bean.FileType.getType;
import static javax.ws.rs.core.Response.ok;
import static org.apache.commons.lang3.LocaleUtils.toLocale;

/**
 * <AUTHOR> GHOZZI <<EMAIL>>
 * @company Generix group
 * @date 17 april 2025
 */
@Service(TranslationService.SERVICE_NAME)
public class TranslationServiceImpl implements TranslationService {

  private static final String FILE_NAME = "translations.csv";

  private final I18NService i18NService;
  private final I18nTranslationDAO i18nTranslationDAO;
  private final DocumentErrorTranslationService documentErrorTranslationService;
  private final AccountService accountService;

  private static final String CODE = "code";
  private static final String MODULE_NAME = "module_name";
  private static final String DELIMITER = ";";
  private static final Set<String> PROTECTED_LANGUAGES = Set.of("fr", "en");

  public TranslationServiceImpl(I18NService i18NService, I18nTranslationDAO i18nTranslationDAO,
      DocumentErrorTranslationService documentErrorTranslationService, AccountService accountService) {
    this.i18NService = i18NService;
    this.i18nTranslationDAO = i18nTranslationDAO;
    this.documentErrorTranslationService = documentErrorTranslationService;
    this.accountService = accountService;
  }

  @Transactional
  @Override
  public Response exportCSVTranslation(File tmpFile){
    List<I18NTranslationDto> translations = i18NService.getTranslations();
    List<Locale> locales = i18NService.getLocales();
    ExcelExportHelper.exportAsCsvGeneric(createExportedData(translations, locales), null, tmpFile, null, null, FILE_NAME, null, generateHeaders(locales));
    return ok(tmpFile, getType(tmpFile).getDefaultMime()).header("Content-Disposition", "attachment; filename=" + tmpFile.getName()).build();
  }

  /**
   * Method to export data
   * @param translations list of translation lines
   * @param availableLocales list of language to export
   * @return list of line to export in csv file
   */
  @Transactional
  public List<String> createExportedData(List<I18NTranslationDto> translations, List<Locale> availableLocales) {
    Map<String, Map<Locale, String>> translationMap = new LinkedHashMap<>();
    for (I18NTranslationDto translation : translations) {
      String code = translation.getCode();
      Locale locale = translation.getLocale();
      String valueToUse = translation.getNewValue()!= null ? translation.getNewValue() : translation.getDefaultValue();
      translationMap
              .computeIfAbsent(code, k -> new HashMap<>())
              .put(locale, valueToUse);
    }
    List<String> exportedData = new ArrayList<>();
    for (String code : translationMap.keySet()) {
      Map<Locale, String> localeMap = translationMap.get(code);
      Query q = new Query(new AndClause(Clauses.equal("code", code)));
      String moduleName = i18nTranslationDAO.search(q).get(0).getI18NModule().getName();
      StringBuilder row = new StringBuilder(moduleName !=null ? moduleName : "");
      row.append(DELIMITER).append(code);
      for (Locale locale : availableLocales) {
        String value = localeMap.getOrDefault(locale, "");
        row.append(DELIMITER).append(value);
      }
      exportedData.add(row.toString());
    }
    return exportedData;
  }

  /**
   * list of headers in file
   * @param availableLocales list of language available
   * @return liste of header for the file to export
   */
  private static String[] generateHeaders(List<Locale> availableLocales) {
    // Create headers array
    String[] headers = new String[availableLocales.size() + 2]; // +2 for the "code" header
    headers[1] = CODE; // First header is "code"
    headers[0] = MODULE_NAME; // second header is "module_id"
    int index = 2;
    for (Locale locale : availableLocales) {
      headers[index++] = locale.toString(); // Add each locale as a header
    }
    return headers;
  }

  @Transactional
  @Override
  public Response importTranslationsData(User user, String locale, Attachment file) {
    Locale messageLocale = new Locale(locale);
    List<List<String>> records = new ArrayList<>();
    Set<String> errorLines = new HashSet<>();
    List<String> successLines = new ArrayList<>();
    String errorLabel = i18NService.findByCodeAndLocale("error_label", messageLocale, user.getFullname());
    try (BufferedReader br = new BufferedReader(new InputStreamReader(file.getDataHandler().getInputStream()))) {
      String line;
      while ((line = br.readLine()) != null) {
        records.add(Arrays.asList(line.split(DELIMITER)));
      }
      Set<String> languages = Arrays.stream(Locale.getISOLanguages())
              .map(Locale::new)
              .map(Locale::getDisplayLanguage)
              .collect(Collectors.toCollection(TreeSet::new));
      for (int recodsLine=1; recodsLine < records.size(); recodsLine++) {
        List<String> record = records.get(recodsLine);
        updateTranslations(records, languages, record, user, locale, errorLines, errorLabel, recodsLine);
      }
      String messageSuccess = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_file_successfully", "", new Locale(locale), records.size() - errorLines.size());
      successLines.add(messageSuccess);
      return listResultMessages(errorLines, successLines, messageSuccess);
    } catch (FileNotFoundException e) {
      throw new RuntimeException(e);
    } catch (IOException e) {
      throw new RuntimeException("Error reading the file: " + e.getMessage(), e);
    }
  }

  /**
   * Exports a CSV file containing error code translations for one or more specified languages.
   *
   * <p>This method retrieves translations from the data source, organizes them by error code and language,
   * and returns the result as a downloadable CSV file. If the provided list of language codes is null or empty,
   * the method automatically includes all available languages present in the data.</p>
   *
   * <p>The resulting CSV will use semicolon (;) as a separator and contain one row per error code.
   * Each row includes the error code followed by its translations in the specified languages.</p>
   *
   * <p>If no translation exists for a given language/errorCode pair, the corresponding cell in the CSV will be left empty.</p>
   *
   * @param requestedLang a list of ISO 639-1 language codes (e.g., "fr", "en", "de").
   *                       If null or empty, all available languages will be included in the export.
   *                       Only valid language codes should be passed; otherwise, validation should be handled externally.
   * @return a JAX-RS {@link Response} containing the CSV file as a byte stream, with headers configured
   *         to trigger a file download named <code>error_translations.csv</code>.
   */
  @Override
  public Response exportTranslationsWithLang(String requestedLang) {

    List<DocumentErrorTranslation> translations = documentErrorTranslationService.getErrorTranslationByLang(requestedLang);

    Map<String, Map<String, String>> errorCodeToLangMap = new TreeMap<>();
    Set<String> allLanguages = new TreeSet<>();

    for (DocumentErrorTranslation translation : translations) {
      String errorCode = translation.getId().getErrorCode();
      String lang = translation.getId().getLanguageCode();
      String label = translation.getErrorLabel();

      allLanguages.add(lang);

      errorCodeToLangMap
          .computeIfAbsent(errorCode, k -> new HashMap<>())
          .put(lang, label);
    }


    List<String> exportLangs = new ArrayList<>();

    if (requestedLang != null && !requestedLang.isBlank()) {
      exportLangs.add(requestedLang.trim());
    } else {
      exportLangs.addAll(allLanguages);
    }

    // Sort languages
    exportLangs.sort(String::compareTo);

    // Build CSV
    StringBuilder sb = new StringBuilder();
    sb.append("key");
    for (String lang : exportLangs) {
      sb.append(";").append(lang);
    }
    sb.append("\n");

    for (Map.Entry<String, Map<String, String>> entry : errorCodeToLangMap.entrySet()) {
      String errorCode = entry.getKey();
      Map<String, String> langMap = entry.getValue();

      sb.append(errorCode);
      for (String lang : exportLangs) {
        String translation = Optional.ofNullable(langMap.get(lang)).orElse("");
        sb.append(";").append(escapeCsv(translation));
      }
      sb.append("\n");
    }

    byte[] csvBytes = sb.toString().getBytes(StandardCharsets.UTF_8);
    return Response.ok(csvBytes, "application/octet-stream")
        .header("Content-Disposition", "attachment; filename=error_translations.csv")
        .header("Content-Length", csvBytes.length)
        .build();
  }

  @Override
  @Transactional
  public Response importTranslationsWithLang(String requestedLang, InputStream uploadedInputStream) {
    UUID requestId = UUID.randomUUID();
    List<ImportError> errors = new ArrayList<>();
    int successCount = 0;

    try {
      LineIterator it = org.apache.commons.io.IOUtils.lineIterator(uploadedInputStream, "UTF-8");

      if (!it.hasNext()) {
        return RestServiceHelper.getResponseOnError(
            Response.Status.BAD_REQUEST.toString(),
            "CSV file is empty",
            Response.Status.BAD_REQUEST,
            requestId
        );
      }

      String headerLine = it.nextLine();
      String[] headers = StringUtils.splitPreserveAllTokens(headerLine, ';');

      if (!"key".equalsIgnoreCase(headers[0].trim())) {
        return RestServiceHelper.getResponseOnError(
            Response.Status.BAD_REQUEST.toString(),
            "CSV must start with a 'key' column followed by language codes",
            Response.Status.BAD_REQUEST,
            requestId
        );
      }

      Set<String> normalizedRequestedLangs = (requestedLang == null || requestedLang.isBlank())
          ? null
          : (!requestedLang.equalsIgnoreCase("fr") && !requestedLang.equalsIgnoreCase("en"))
              ? Set.of(requestedLang.toLowerCase())
              : Collections.emptySet();


      Map<String, Integer> langColumnIndex = new HashMap<>();
      for (int i = 1; i < headers.length; i++) {
        String langCode = headers[i].trim().toLowerCase();
        if (!LanguageConstants.ALLOWED_LANG_CODES.contains(langCode)) continue;
        if (langCode.equals("fr") || langCode.equals("en")) continue;
        if (normalizedRequestedLangs == null || normalizedRequestedLangs.contains(langCode)) {
          langColumnIndex.put(langCode, i);
        }
      }

      if (langColumnIndex.isEmpty()) {
        return RestServiceHelper.getResponseOnError(
            Response.Status.BAD_REQUEST.toString(),
            "No supported language columns found for import",
            Response.Status.BAD_REQUEST,
            requestId
        );
      }

      int lineNumber = 1;
      while (it.hasNext()) {
        lineNumber++;
        String line = it.nextLine().trim();
        if (line.isEmpty()) continue;

        String[] row = StringUtils.splitPreserveAllTokens(line, ';');

        try {
          String key = row[0].trim();
          if (StringUtils.isBlank(key)) {
            throw new IllegalArgumentException("Missing key at line " + lineNumber);
          }

          DocumentErrorTranslation referenceTranslation = null;
          if (documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "fr")) != null) {
            referenceTranslation = documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "fr"));
          } else if (documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "en")) != null) {
            referenceTranslation = documentErrorTranslationService.getErrorTranslationById(new DocumentErrorTranslationId(key, "en"));
          }

          if (referenceTranslation == null) {
            throw new IllegalArgumentException("Unknown key: '" + key + "' at line " + lineNumber);
          }

          boolean standardError = referenceTranslation.isStandardError();

          for (Map.Entry<String, Integer> entry : langColumnIndex.entrySet()) {
            String langCode = entry.getKey();

            if (!LanguageConstants.ALLOWED_LANG_CODES.contains(langCode)) {
              continue;
            }

            int columnIndex = entry.getValue();
            if (columnIndex >= row.length) continue;

            String label = row[columnIndex].trim();
            if (StringUtils.isBlank(label)) continue;

            DocumentErrorTranslation entity = new DocumentErrorTranslation(
                key,
                langCode,
                label,
                standardError
            );
            documentErrorTranslationService.saveErrorTranslation(entity);
            successCount++;
          }

        } catch (Exception e) {
          errors.add(new ImportError(lineNumber, e.getMessage()));
        }
      }

      DictionaryImportResponse response = new DictionaryImportResponse(successCount, errors);
      return Response.ok(response).build();

    } finally {
      try {
        uploadedInputStream.close();
      } catch (IOException ignored) {}
    }
  }







  /**
   * Escapes a string value for safe inclusion in a CSV field using semicolon (;) as a delimiter.
   *
   * <p>If the input string contains special characters such as semicolon (;), double quote ("), newline (\n),
   * or carriage return (\r), the method wraps the string in double quotes and escapes any existing
   * double quotes by doubling them (i.e., " becomes "").</p>
   *
   * <p>This ensures the output conforms to standard CSV formatting rules.</p>
   *
   * @param value the input string to escape for CSV output
   * @return the escaped string, safely formatted for CSV inclusion
   */
  private String escapeCsv(String value) {
    if (value.contains(";") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
      value = value.replace("\"", "\"\"");
      return "\"" + value + "\"";
    }
    return value;
  }


  /**
   * Method to update line in DB
   * @param resultTranslationsList list of retrived translation line
   * @param record list of language in line
   * @param user connected user
   * @param position poisition of language
   */
  private void updateRow(List<I18NTranslation> resultTranslationsList, List<String> record, User user, int position){
    I18NTranslation i18NTranslation = resultTranslationsList.get(0);
    i18NTranslation.setNewValue(record.get(position));
    i18NTranslation.setNewValueChangedAt(new Date());
    i18NTranslation.setNewValueChangedBy(user);
    i18nTranslationDAO.merge(i18NTranslation);
  }

  /**
   * Method to sort messages error messages
   * @param errorSet set of error message
   * @return list of error messages sorted
   */
  private static List<String> sortErrorList(Set<String> errorSet) {
    List<String> errorList = new ArrayList<>(errorSet);
    Collections.sort(errorList);
    return errorList;
  }

  /**
   * Method to return result listMessages
   * @param errorLines list of errors
   * @param successLines list of succes updated line
   * @param messageSuccess message of succeded update
   * @return response on method
   */
  private Response listResultMessages(Set<String> errorLines, List<String> successLines, String messageSuccess){
    if (!errorLines.isEmpty() && successLines.isEmpty()) {
      List<String> sortedErrorList =  sortErrorList(errorLines);
      return Response.status(Response.Status.NOT_ACCEPTABLE)
              .entity(String.join("\n", sortedErrorList))
              .build();
    } else if (!errorLines.isEmpty()) {
      List<String> sortedErrorList =  sortErrorList(errorLines);
      String responseMessage = String.join("\n", successLines) + "\n" +
              String.join("\n", sortedErrorList);
      return Response.status(Response.Status.NOT_ACCEPTABLE)
              .entity(responseMessage)
              .build();
    } else {
      return Response.ok(messageSuccess).build();
    }
  }

  /**
   * Method to iterate list of translation lines in CSV imported file
   * @param records traslation line
   * @param languages locale
   * @param record list of locale to translate
   * @param user user connected
   * @param locale local ofcurrent user
   * @param errorLines set of errors
   * @param errorLabel error label
   * @param recodsLine list of translations line
   */
  private void updateTranslations(List<List<String>> records, Set<String> languages, List<String> record, User user, String locale, Set<String> errorLines, String errorLabel, int recodsLine){
    // start from the 3th position in the list to not take module_name and code
    for (int recordPosition=2; recordPosition < record.size(); recordPosition++) {
      if(!record.get(recordPosition).isEmpty()) {
        String localeFromRecord = records.get(0).get(recordPosition);
        String currentLocale = toLocale(localeFromRecord).getDisplayLanguage();
        List<String> locales = languages.stream().filter(language -> language.equals(currentLocale)).toList();
        if (locales.isEmpty()) {
          String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_locale_inconnue", "", new Locale(locale), recordPosition + 1, currentLocale);
          errorLines.add(errorLabel + localeInconnue);
          break;
        } else {
          if (!record.get(recordPosition).isEmpty()) {
            // search elements with locale and code
            Query q = new Query(new AndClause(Clauses.equal("locale", localeFromRecord), Clauses.equal("code", record.get(1))));
            List<I18NTranslation> resultTranslationsList = i18nTranslationDAO.search(q);
            if (!resultTranslationsList.isEmpty()) {
              I18NTranslation i18NTranslation = new I18NTranslation();
              Optional<I18NModule> i18nModule = i18NService.findI18NModuleByName(record.get(0));
              if (i18nModule.isPresent()) {
                i18NTranslation.setI18NModule(i18nModule.get());
              } else {
                String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_ligne_ignoree", "", new Locale(locale), recodsLine, currentLocale);
                errorLines.add(errorLabel + localeInconnue);
                break;
              }
              // update data in i18n_translation table
              updateRow(resultTranslationsList, record, user, recordPosition);
            } else {
              String localeInconnue = MessageHelper.getMessage("gnxxcblcomlbls" + "." + "import_translation_ligne_ignoree", "", new Locale(locale), recodsLine, record.get(0), record.get(1));
              errorLines.add(errorLabel + localeInconnue);
              break;
            }
          }
        }
      }
    }
  }

  @Override
  @Transactional
  public Response importFrontendTranslationsWithLang(String requestedLang, InputStream csvContent) {
    UUID requestId = UUID.randomUUID();
    var errors = new ArrayList<ImportError>();
    var totalImported = 0;

    try {
      // Enhanced CSV parsing with proper encoding handling for special characters
      var csvContentString = new String(csvContent.readAllBytes(), StandardCharsets.UTF_8);
      // Normalize line endings and split
      var lines = csvContentString.replace("\r\n", "\n").replace("\r", "\n").split("\n");

      if (lines.length < 2) {
        var error = ImportError.builder()
            .lineNumber(0)
            .errorMessage("File must contain at least a header and one data row")
            .build();
        errors.add(error);

        var response = DictionaryImportResponse.builder()
            .totalImported(0)
            .errors(errors)
            .build();

        return Response.status(Response.Status.CREATED).entity(response).build();
      }

      totalImported = processFrontendCsvLines(lines, requestedLang, errors);

      var response = DictionaryImportResponse.builder()
          .totalImported(totalImported)
          .errors(errors)
          .build();

      return Response.status(Response.Status.CREATED).entity(response).build();

    } catch (Exception e) {
      return RestServiceHelper.getResponseOnError(
          Response.Status.INTERNAL_SERVER_ERROR.toString(),
          "Failed to import frontend translations: " + e.getMessage(),
          Response.Status.INTERNAL_SERVER_ERROR,
          requestId
      );
    }
  }

  private int processFrontendCsvLines(String[] lines, String requestedLang, List<ImportError> errors) {
    var totalImported = 0;
    var headers = parseCsvLine(lines[0]);

    if (headers.length < 3 || !"module".equals(headers[0]) || !"key".equals(headers[1])) {
      errors.add(ImportError.builder()
          .lineNumber(1)
          .errorMessage("Invalid header format. Expected: module;key;language1;language2;...")
          .build());
      return 0;
    }

    // Enhanced locale validation with detailed error reporting
    var langColumnIndex = new HashMap<String, Integer>();
    for (var i = 2; i < headers.length; i++) {
      var langCode = headers[i].trim().toLowerCase();

      // Check if locale is valid
      if (!LanguageConstants.ALLOWED_LANG_CODES.contains(langCode)) {
        errors.add(ImportError.builder()
            .lineNumber(1)
            .errorMessage("Error [column " + (i + 1) + "]: The locale " + langCode + " is not a recognized locale. The column is ignored.")
            .build());
        continue;
      }

      if (requestedLang == null || requestedLang.isBlank() || requestedLang.equalsIgnoreCase(langCode)) {
        langColumnIndex.put(langCode, i);
      }
    }

    // Process data rows
    for (var lineNumber = 2; lineNumber <= lines.length; lineNumber++) {
      var line = lines[lineNumber - 1].trim();
      if (line.isEmpty()) continue;

      var parts = parseCsvLine(line);
      if (parts.length < 3) {
        errors.add(ImportError.builder()
            .lineNumber(lineNumber)
            .errorMessage("Row must have at least module, key, and one translation")
            .build());
        continue;
      }

      var module = parts[0].trim();
      var key = parts[1].trim();

      if (module.isEmpty() || key.isEmpty()) {
        errors.add(ImportError.builder()
            .lineNumber(lineNumber)
            .errorMessage("Module and key cannot be empty")
            .build());
        continue;
      }

      totalImported += processTranslationRow(module, key, parts, langColumnIndex, errors, lineNumber);
    }

    return totalImported;
  }

  private int processTranslationRow(String module, String key, String[] parts, Map<String, Integer> langColumnIndex,
                                  List<ImportError> errors, int lineNumber) {
    var imported = 0;

    // Find module
    var moduleOpt = i18NService.findI18NModuleByName(module);
    if (moduleOpt.isEmpty()) {
      errors.add(ImportError.builder()
          .lineNumber(lineNumber)
          .errorMessage("Error [line " + lineNumber + "]: Code " + key + " (Module Name = " + module + ") does not exist. The line is ignored.")
          .build());
      return 0;
    }

    var moduleEntity = moduleOpt.get();

    // Enhanced validation: Check if key exists for French and English
    if (!isKeyExistingForProtectedLanguages(moduleEntity.getId(), key)) {
      errors.add(ImportError.builder()
          .lineNumber(lineNumber)
          .errorMessage("Error [line " + lineNumber + "]: Code " + key + " (Module Name = " + module + ") does not exist. The line is ignored.")
          .build());
      return 0;
    }

    for (var entry : langColumnIndex.entrySet()) {
      var langCode = entry.getKey();
      var columnIndex = entry.getValue();

      if (columnIndex >= parts.length) continue;

      var value = parts[columnIndex].trim();
      if (value.isEmpty()) continue;

      if (PROTECTED_LANGUAGES.contains(langCode)) {
        continue;
      }

      try {
        updateOrCreateTranslation(moduleEntity.getId(), key, langCode, value);
        imported++;
      } catch (Exception e) {
        errors.add(ImportError.builder()
            .lineNumber(lineNumber)
            .errorMessage("Failed to import translation for key '" + key + "' and language '" + langCode + "': " + e.getMessage())
            .build());
      }
    }

    return imported;
  }

  private void updateOrCreateTranslation(Long moduleId, String key, String langCode, String value) {
    var locale = new Locale(langCode);

    User actualUser = null;
    try {
      // For service layer, we'll use admin user as fallback since we don't have HttpServletRequest
      actualUser = accountService.getUserByLoginOrOpenID("admin");
    } catch (Exception e) {
      // Log warning but continue
    }

    if (actualUser == null) {
      throw new RuntimeException("No user found for tracking translation changes");
    }

    // Find existing translation using correct property names
    var query = new Query(new AndClause(
        Clauses.equal("code", key),
        Clauses.equal("locale", locale),
        Clauses.equal("i18NModule.id", moduleId)
    ));

    var existingTranslations = i18nTranslationDAO.search(query);

    if (!existingTranslations.isEmpty()) {
      // Update existing
      var existing = existingTranslations.get(0);
      existing.setNewValue(value);
      existing.setNewValueChangedAt(new Date());
      existing.setNewValueChangedBy(actualUser);
      i18nTranslationDAO.merge(existing);
    } else {
      // Create new translation
      var newTranslation = new I18NTranslation();
      newTranslation.setCode(key);
      newTranslation.setLocale(locale);
      newTranslation.setNewValue(value);
      newTranslation.setNewValueChangedAt(new Date());
      newTranslation.setNewValueChangedBy(actualUser);

      var module = i18NService.findI18NModuleById(moduleId);
      newTranslation.setI18NModule(module);

      i18nTranslationDAO.store(newTranslation);
    }
  }

  private boolean isKeyExistingForProtectedLanguages(Long moduleId, String key) {
    for (String protectedLang : PROTECTED_LANGUAGES) {
      var query = new Query(new AndClause(
          Clauses.equal("code", key),
          Clauses.equal("locale", new Locale(protectedLang)),
          Clauses.equal("i18NModule.id", moduleId)
      ));

      var existingTranslations = i18nTranslationDAO.search(query);
      if (existingTranslations.isEmpty()) {
        return false; // Key doesn't exist for this protected language
      }
    }
    return true; // Key exists for all protected languages
  }

  @Override
  public Response exportFrontendTranslationsWithLang(String requestedLang) {
    try {
      var translations = i18NService.getTranslations();
      var availableLocales = i18NService.getLocales();

      var exportLocales = requestedLang != null && !requestedLang.isBlank()
          ? availableLocales.stream()
              .filter(locale -> requestedLang.equalsIgnoreCase(locale.getLanguage()))
              .toList()
          : availableLocales;

      var csvContent = generateFrontendCsv(translations, exportLocales);
      var csvBytes = csvContent.getBytes(StandardCharsets.UTF_8);

      var filename = "frontend_translations.csv";

      return Response.ok(csvBytes, "application/octet-stream")
          .header("Content-Disposition", "attachment; filename=" + filename)
          .header("Content-Length", csvBytes.length)
          .build();

    } catch (Exception e) {
      throw new RuntimeException("Failed to export frontend translations: " + e.getMessage(), e);
    }
  }

  private String generateFrontendCsv(List<I18NTranslationDto> translations, List<Locale> exportLocales) {
    var csvBuilder = new StringBuilder();

    var sortedLocales = exportLocales.stream()
        .sorted(Comparator.comparing(Locale::toString))
        .toList();

    csvBuilder.append("module").append(DELIMITER).append("key");
    for (var locale : sortedLocales) {
      csvBuilder.append(DELIMITER).append(locale.toString());
    }
    csvBuilder.append("\n");

    // First, we need to get module names for each translation
    var moduleMap = new HashMap<Long, String>();
    var allModules = i18NService.findAllI18NModules();
    allModules.forEach(module -> moduleMap.put(module.getId(), module.getName()));

    var translationMap = translations.stream()
        .filter(t -> moduleMap.containsKey(t.getI18NModuleId()))
        .collect(Collectors.groupingBy(
            t -> moduleMap.get(t.getI18NModuleId()) + ":" + t.getCode(),
            Collectors.toMap(
                t -> t.getLocale().toString(),
                t -> t.getNewValue() != null ? t.getNewValue() : (t.getDefaultValue() != null ? t.getDefaultValue() : ""),
                (existing, replacement) -> replacement
            )
        ));

    translationMap.entrySet().stream()
        .sorted(Map.Entry.comparingByKey())
        .forEach(entry -> {
          var moduleKey = entry.getKey().split(":", 2);
          var module = moduleKey[0];
          var key = moduleKey[1];
          var localeValues = entry.getValue();

          csvBuilder.append(module).append(DELIMITER).append(key);
          for (var locale : sortedLocales) {
            var value = localeValues.getOrDefault(locale.toString(), "");
            csvBuilder.append(DELIMITER).append(escapeCsvValue(value));
          }
          csvBuilder.append("\n");
        });

    return csvBuilder.toString();
  }

  private String[] parseCsvLine(String line) {
    var parts = new ArrayList<String>();
    var current = new StringBuilder();
    var inQuotes = false;
    var i = 0;

    while (i < line.length()) {
      var c = line.charAt(i);

      if (c == '"') {
        if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
          // Escaped quote
          current.append('"');
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (c == ';' && !inQuotes) {
        // Field separator
        parts.add(current.toString());
        current.setLength(0);
        i++;
      } else {
        current.append(c);
        i++;
      }
    }

    // Add the last field
    parts.add(current.toString());

    return parts.toArray(new String[0]);
  }

  private String escapeCsvValue(String value) {
    if (value == null) return "";

    return value.contains(DELIMITER) || value.contains("\"") || value.contains("\n")
        ? "\"" + value.replace("\"", "\"\"") + "\""
        : value;
  }

}
